"""
Unit tests for document set persona access validation functionality.

Tests the validation logic that prevents document set reassignment when
personas from other teams have access to the document set.
"""

import pytest
from unittest.mock import <PERSON><PERSON>, MagicMock
from sqlalchemy.orm import Session

from onyx.db.document_set import _check_persona_access_for_document_set_reassignment
from onyx.db.models import Persona, Persona__DocumentSet, Persona__UserGroup


class TestDocumentSetPersonaValidation:
    """Test cases for document set persona access validation."""

    def test_no_personas_with_access_allows_reassignment(self):
        """Test that reassignment is allowed when no personas have access."""
        # Mock database session
        db_session = Mock(spec=Session)
        
        # Mock query to return no personas
        mock_query = Mock()
        mock_query.join.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        db_session.query.return_value = mock_query
        
        # Should not raise any exception
        _check_persona_access_for_document_set_reassignment(
            db_session=db_session,
            document_set_id=1,
            new_user_teams_ids=[2, 3],
            current_user_teams_ids=[1]
        )

    def test_personas_from_same_teams_allows_reassignment(self):
        """Test that reassignment is allowed when personas are from the same teams."""
        # Mock database session
        db_session = Mock(spec=Session)
        
        # Create mock personas
        persona1 = Mock(spec=Persona)
        persona1.id = 1
        persona1.name = "Test Persona 1"
        
        persona2 = Mock(spec=Persona)
        persona2.id = 2
        persona2.name = "Test Persona 2"
        
        # Mock query to return personas
        mock_query = Mock()
        mock_query.join.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [persona1, persona2]
        
        # Mock team queries for personas
        def mock_query_side_effect(*args):
            if args[0] == Persona:
                return mock_query
            elif args[0] == Persona__UserGroup.user_group_id:
                # Return different mock for team queries
                team_query = Mock()
                team_query.filter.return_value = team_query
                if hasattr(team_query, '_persona_id'):
                    if team_query._persona_id == 1:
                        team_query.all.return_value = [(2,), (3,)]  # Teams 2, 3
                    else:
                        team_query.all.return_value = [(2,)]  # Team 2
                else:
                    # Set up the filter to track persona_id
                    def filter_func(condition):
                        if hasattr(condition, 'right') and hasattr(condition.right, 'value'):
                            team_query._persona_id = condition.right.value
                        return team_query
                    team_query.filter = filter_func
                    team_query.all.return_value = [(2,), (3,)]  # Default
                return team_query
            return mock_query
        
        db_session.query.side_effect = mock_query_side_effect
        
        # Should not raise any exception since all persona teams (2, 3) are in new teams [2, 3]
        _check_persona_access_for_document_set_reassignment(
            db_session=db_session,
            document_set_id=1,
            new_user_teams_ids=[2, 3],
            current_user_teams_ids=[1]
        )

    def test_personas_from_different_teams_blocks_reassignment(self):
        """Test that reassignment is blocked when personas are from different teams."""
        # Mock database session
        db_session = Mock(spec=Session)
        
        # Create mock personas
        persona1 = Mock(spec=Persona)
        persona1.id = 1
        persona1.name = "Test Persona 1"
        
        # Mock query to return personas
        mock_query = Mock()
        mock_query.join.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [persona1]
        
        # Mock team query for persona
        team_query = Mock()
        team_query.filter.return_value = team_query
        team_query.all.return_value = [(1,)]  # Persona is in team 1
        
        def mock_query_side_effect(*args):
            if args[0] == Persona:
                return mock_query
            elif args[0] == Persona__UserGroup.user_group_id:
                return team_query
            return mock_query
        
        db_session.query.side_effect = mock_query_side_effect
        
        # Should raise ValueError since persona team (1) is not in new teams [2, 3]
        with pytest.raises(ValueError) as exc_info:
            _check_persona_access_for_document_set_reassignment(
                db_session=db_session,
                document_set_id=1,
                new_user_teams_ids=[2, 3],
                current_user_teams_ids=[1]
            )
        
        assert "Cannot reassign document set to different teams" in str(exc_info.value)
        assert "Test Persona 1" in str(exc_info.value)

    def test_making_document_set_public_with_persona_access_blocks(self):
        """Test that making document set public is blocked when personas have access."""
        # Mock database session
        db_session = Mock(spec=Session)
        
        # Create mock personas
        persona1 = Mock(spec=Persona)
        persona1.id = 1
        persona1.name = "Test Persona 1"
        
        # Mock query to return personas
        mock_query = Mock()
        mock_query.join.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [persona1]
        
        # Mock team query for persona
        team_query = Mock()
        team_query.filter.return_value = team_query
        team_query.all.return_value = [(1,)]  # Persona is in team 1
        
        def mock_query_side_effect(*args):
            if args[0] == Persona:
                return mock_query
            elif args[0] == Persona__UserGroup.user_group_id:
                return team_query
            return mock_query
        
        db_session.query.side_effect = mock_query_side_effect
        
        # Should raise ValueError when trying to make document set public (empty new_user_teams_ids)
        with pytest.raises(ValueError) as exc_info:
            _check_persona_access_for_document_set_reassignment(
                db_session=db_session,
                document_set_id=1,
                new_user_teams_ids=[],  # Empty means making it public
                current_user_teams_ids=[1]
            )
        
        assert "Cannot make document set public" in str(exc_info.value)
        assert "Test Persona 1" in str(exc_info.value)

    def test_making_document_set_public_without_persona_access_allows(self):
        """Test that making document set public is allowed when no personas have access."""
        # Mock database session
        db_session = Mock(spec=Session)
        
        # Mock query to return no personas
        mock_query = Mock()
        mock_query.join.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        db_session.query.return_value = mock_query
        
        # Should not raise any exception when making document set public with no persona access
        _check_persona_access_for_document_set_reassignment(
            db_session=db_session,
            document_set_id=1,
            new_user_teams_ids=[],  # Empty means making it public
            current_user_teams_ids=[1]
        )
